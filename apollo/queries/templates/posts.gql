query TemplatePost($locale: I18NLocaleCode!, $publicationState: PublicationState = LIVE) {
  templatePosts(locale: $locale, publicationState:$publicationState, sort: "publishedDate:desc", pagination: { limit: -1 }) {
    data {
      id
      attributes {
        backend_id
        createdAt
        updatedAt
        title
        subtitle
        content
        slug
        publishedDate
        thumbnails {
          data {
            attributes {
              formats
              blurhash
              alternativeText
            }
          }
        }
        template_categories {
          data {
            attributes {
              title
            }
          }
        }
      }
    }
  }
}
