<template>
  <div class="my-auto">
    <!-- Modal Background -->
    <div
      class="fixed inset-0 bg-body-black bg-opacity-75 transition-opacity z-40"
      aria-hidden="true"></div>

    <!-- Use Template Button -->
    <div
      class="w-full h-16 fixed md:hidden bottom-0 left-0 right-0 flex space-x-4 justify-center items-center bg-body-gray border-t z-[55]">
      <a :href="`https://app.onvocado.com/register?templateId=${template.id}`" class="button secondary">
        Use this template
      </a>
    </div>

    <!-- Modal Container -->
    <div class="fixed inset-0 z-50">
      <div class="flex items-center justify-center min-h-screen md:pt-4 md:px-4 md:pb-20 text-center sm:p-0"
           @click="$emit('close')">

        <!-- Modal Content -->
        <div
          class="max-h-screen lg:max-h-[calc(100vh-110px)] overflow-none md:overflow-y-auto relative bg-body-gray rounded-xl text-left shadow-xl transform transition-all w-full max-w-6xl"
          data-lenis-prevent
          @click.stop>

          <!-- Modal Controls -->
          <div class="md:px-4 my-4 flex justify-between container">
            <div>
              <div class="flex gap-8 items-center">
                <NuxtLink
                  :to="`${$i18n.locale === $i18n.defaultLocale ? '' : '/' + $i18n.locale}${i18nPages['templates/_slug'][$i18n.locale].replace(':slug', template.attributes?.slug)}`">
                  <svg xmlns="http://www.w3.org/2000/svg" height="26px" viewBox="0 -960 960 960" width="26px"
                       fill="#0f2815">
                    <path d="M144-144v-288h72v165l477-477H528v-72h288v288h-72v-165L267-216h165v72H144Z"/>
                  </svg>
                </NuxtLink>

                <hr class="w-0.5 h-6 bg-body-black opacity-30">

                <div class="flex items-center gap-2">
                  <button :class="{'opacity-30 pointer-events-none': currentIndex === 0 }"
                          :disabled="currentIndex === 0"
                          type="button"
                          @click="changeTemplate(currentIndex - 1 )">
                    <svg xmlns="http://www.w3.org/2000/svg" height="26px" viewBox="0 -960 960 960" width="26px"
                         fill="#0f2815">
                      <path d="m313-440 224 224-57 56-320-320 320-320 57 56-224 224h487v80H313Z"/>
                    </svg>
                  </button>
                  <button type="button"
                          :class="{'opacity-30 pointer-events-none': (currentIndex + 1) === totalTemplates }"
                          :disabled="currentIndex === totalTemplates"
                          @click="changeTemplate(currentIndex + 1 )">
                    <svg xmlns="http://www.w3.org/2000/svg" height="26px" viewBox="0 -960 960 960" width="26px"
                         fill="#0f2815">
                      <path d="M630-444H192v-72h438L429-717l51-51 288 288-288 288-51-51 201-201Z"/>
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            <!-- Modal Close Button -->
            <button
              type="button"
              class="text-gray-400 hover:text-gray-500"
              @click="$emit('close')">
              <span class="sr-only">Close</span>
              <svg xmlns="http://www.w3.org/2000/svg" height="26px" viewBox="0 -960 960 960" width="26px"
                   fill="#0f2815">
                <path
                  d="m291-240-51-51 189-189-189-189 51-51 189 189 189-189 51 51-189 189 189 189-51 51-189-189-189 189Z"/>
              </svg>
            </button>
          </div>

          <!-- Modal Body -->
          <div
            class="max-w-5xl max-h-[calc(100vh-62px)] md:max-h-none pt-5 pb-12 sm:p-6 mx-auto overflow-y-auto md:overflow-none">
            <div class="mb-16 container">
              <!-- Header -->
              <div class="flex justify-between items-end md:items-center">
                <!-- Title -->
                <h1 v-if="template.attributes.title" class="w-3/4 md:w-1/2 text-3xl lg:text-4xl 2xl:text-5xl font-bold">
                  {{ template.attributes.title }}
                </h1>

                <!-- Buttons -->
                <div class="w-1/2 hidden md:flex space-x-4 justify-end items-center">
                  <a :href="`https://app.onvocado.com/register?templateId=${template.id}`" class="button secondary">
                    Use this template
                  </a>
                </div>

              </div>

              <!-- Template Preview and Description Section -->
              <div class="grid grid-cols-12 gap-6 mt-6">
                <!-- Template Preview Section -->
                <div class="col-span-12 sm:col-span-7 flex flex-col">
                  <!-- Large Preview using WidgetStepPreview -->
                  <div
                    class="w-full relative shadow-2xl rounded-lg border bg-body-gray-dark aspect-16/10 overflow-hidden origin-top-left">
                    <WidgetStepPreview
                      v-if="template.steps && template.steps.length"
                      :widget="fakeTemplate"
                      :target-step-index="targetImgIndex"
                      class="w-full"/>
                  </div>

                  <section class="p-2 md:p-4 lg:p-6 grid grid-cols-4 gap-2 md:gap-4">
                    <button
                      v-for="(img, index) in template.attributes.thumbnails.data"
                      :key="index"
                      class="relative block overflow-hidden rounded-lg shadow-md hover:shadow-lg bg-body-gray-dark"
                      @click="setTargetImage(index)"
                      :class="{'border-2 border-onvocado-primary': targetImgIndex === index}">
                       <div class="w-full relative bg-body-gray-dark aspect-16/10 overflow-hidden origin-top-left">
                        <WidgetStepPreview :widget="fakeTemplate"
                                           :target-step-index="index"
                                           class="w-full"/>
                      </div>
                    </button>
                  </section>
                </div>

                <!-- Description & Buttons -->
                <div class="col-span-12 sm:col-span-5">
                  <!-- About the Template -->
                  <div class="text-gray-700 mb-4 markdown" v-html="template.attributes.content">
                  </div>

                  <!-- Template Info -->
                  <div class="text-sm mb-4">
                    <h4 class="font-semibold">Categories</h4>
                    <ul v-for="(category, index) in template.attributes.template_categories.data"
                        class="text-gray-500"
                        :key="index">
                      <li>{{ category.attributes.title }}</li>
                    </ul>
                  </div>
                </div>

              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import WidgetStepPreview from '@/libs/widget-step-preview.common.js'
import '@/libs/widget-step-preview.css'
import {i18nPages} from '@/i18n.config.js'

export default {
  name: "TemplateModal",
  props: {
    template: {
      type: Object,
      required: true,
    },
    totalTemplates: {
      type: Number,
      required: true,
    },
    currentIndex: {
      type: Number,
      required: true,
    },
  },
  components: {
    WidgetStepPreview,
  },
  data() {
    return {
      targetImgIndex: 0, // Start with the first image
      i18nPages,
      // #templateImpro: Replaced fakeTemplate with real widget template data
      widgetTemplate: null,
      isLoadingTemplate: false,
      templateError: null,

    };
  },
  async mounted() {
    // #templateImpro: Fetch widget template data when component mounts
    await this.fetchWidgetTemplate();
  },
  watch: {
    // #templateImpro: Watch for template changes and refetch widget data
    'template.attributes.backend_id': {
      handler: 'fetchWidgetTemplate',
      immediate: false
    }
  },
  methods: {
    // #templateImpro: Method to fetch widget template data
    async fetchWidgetTemplate() {
      if (!this.template?.attributes?.backend_id) {
        console.warn('No backend_id found for template');
        return;
      }

      this.isLoadingTemplate = true;
      this.templateError = null;

      try {
        const widgetTemplate = await this.$axios.$get(`/v1/api/widget-templates/${this.template.attributes.backend_id}/`);
        this.widgetTemplate = widgetTemplate;
      } catch (error) {
        console.error(`Failed to fetch widget template ${this.template.attributes.backend_id}:`, error);
        this.templateError = error;
        this.widgetTemplate = null;
      } finally {
        this.isLoadingTemplate = false;
      }
    },
    setTargetImage(index) {
      if (index >= 0 && index < this.template.attributes.thumbnails.data.length) {
        this.targetImgIndex = index;
      }
    },
    changeTemplate(newIndex) {
      if (newIndex >= 0 && newIndex < this.totalTemplates) {
        this.$emit("template-change", newIndex);
      } else {
        console.warn(`Template index out of bounds: ${newIndex}`);
      }
    },
  }
};
</script>
