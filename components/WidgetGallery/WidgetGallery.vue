<template>
  <div class="flex flex-wrap">
    <div
      v-for="template in templates.data"
      :key="template.attributes.backend_id"
      class="w-full sm:w-1/2 lg:w-1/3 px-2 mb-2"
      @click="triggerPreviewWidget(template.attributes.backend_id)">
      <div :class="$style.widgetDetailsContainer">
        <div
          class="w-full relative block cursor-pointer border-[3px] rounded-xl shadow overflow-hidden transition-colors duration-500 origin-top-left bg-body-gray-dark"
          :class="$style.widgetThumbnail">

          <!-- #templateImpro: Show placeholder for gallery view, real template will be loaded in modal -->
          <div class="w-full h-full flex items-center justify-center text-gray-500">
            <div class="text-center">
              <div class="text-2xl mb-2">🎨</div>
              <div class="text-sm">Template Preview</div>
            </div>
          </div>

        </div>

        <div class="w-full p-2">
          <h4 class="font-bold">{{ template.attributes.title }}</h4>

          <ul :class="$style.tags" class="mb-2 space-x-1 space-y-1">
            <li v-for="(tag, index) in template.attributes.template_categories.data"
                :key="index"
                class="text-sm inline-flex items-center leading-sm text-onvocado-primary-dark rounded-full">
              {{ tag.attributes.title | snakeToTitleCase }}
            </li>
          </ul>

          <span class="mb-1 text-sm text-onvocado-gray line-clamp-3">{{ template.attributes.subtitle }}</span>
        </div>
      </div>
    </div>

    <div
      v-if="templates.data.length === 0"
      class="w-full px-2 text-center">
      <div class="rounded-xl bg-transparent text-gray-500">
        No Match Found
      </div>
    </div>
  </div>
</template>

<script src="./_widgetGallery.js"></script>

<style lang="scss" src="./_widgetGallery.scss" module></style>

<style lang="scss">
.widget-preview {
  #onvocado-root.is-mobile.hidden-mobile {
    display: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    pointer-events: none !important;
  }

  .onvocado-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    visibility: visible;
    overflow: hidden;
    z-index: 2147483600;
    pointer-events: none;
  }

  .widget-item {
    margin: 0;
    padding: 0;
    border: 0;
    box-sizing: content-box !important;
    background-clip: padding-box !important;
    line-height: 1.2;
    vertical-align: middle;
    text-align: left;
    font-weight: 400;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-style: normal;
    letter-spacing: normal;
    text-shadow: none;
    text-transform: none;
    float: none;
    pointer-events: all;
    z-index: 2147483635;
    -webkit-font-smoothing: subpixel-antialiased;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    filter: none;
  }
}
</style>
