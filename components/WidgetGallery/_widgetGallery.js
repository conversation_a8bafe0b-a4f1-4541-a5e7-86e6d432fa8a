import WidgetStepPreview from '@/libs/widget-step-preview.common.js'
import '@/libs/widget-step-preview.css'

export default {
  name: "WidgetGallery",
  components: {
    WidgetStepPreview,
  },
  props: {
    templates: {
      type: Object,
      required: true,
    },
    hidePreview: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
  data() {
    return {
      // #templateImpro: Replaced fakeTemplate with cache for widget templates
      widgetTemplateCache: new Map(),
      loadingTemplates: new Set(),


    }
  },
  filters: {
    snakeToTitleCase: function (value) {
      if (!value) return ''
      return value.split('-').map(function (item) {
        return item.charAt(0).toUpperCase() + item.substring(1)
      }).join(' ')
    },
  },
  methods: {
    // #templateImpro: Method to fetch widget template by backend_id with caching
    async getWidgetTemplate(backendId) {
      // Check cache first
      if (this.widgetTemplateCache.has(backendId)) {
        return this.widgetTemplateCache.get(backendId);
      }

      // Check if already loading
      if (this.loadingTemplates.has(backendId)) {
        // Wait for the existing request to complete
        return new Promise((resolve) => {
          const checkCache = () => {
            if (this.widgetTemplateCache.has(backendId)) {
              resolve(this.widgetTemplateCache.get(backendId));
            } else if (!this.loadingTemplates.has(backendId)) {
              resolve(null); // Failed to load
            } else {
              setTimeout(checkCache, 100);
            }
          };
          checkCache();
        });
      }

      // Start loading
      this.loadingTemplates.add(backendId);

      try {
        const widgetTemplate = await this.$axios.$get(`/v1/api/widget-templates/${backendId}/`);
        this.widgetTemplateCache.set(backendId, widgetTemplate);
        return widgetTemplate;
      } catch (error) {
        console.error(`Failed to fetch widget template ${backendId}:`, error);
        this.widgetTemplateCache.set(backendId, null); // Cache the failure
        return null;
      } finally {
        this.loadingTemplates.delete(backendId);
      }
    },
    triggerPreviewWidget(id) {
      this.$emit('preview-widget', id)
    },
  },
}
