<template>
  <PreviewModeWrapper :previewMode="previewMode">
    <!-- Menu spacer -->
    <MenuSpacer/>

    <template v-if="templatePost">
      <section class="pt-8 mb-12 flex">
        <div class="mb-4 mx-4 md:pb-8 flex justify-between items-center">
          <NuxtLink :to="$localePathWithTrailingSlash('/templates/')">
            <div class="flex items-center gap-2 text-body-black font-bold text-sm">
              <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px"
                   fill="#0f2815">
                <path
                  d="m260-520 220-360 220 360H260ZM700-80q-75 0-127.5-52.5T520-260q0-75 52.5-127.5T700-440q75 0 127.5 52.5T880-260q0 75-52.5 127.5T700-80Zm-580-20v-320h320v320H120Zm580-60q42 0 71-29t29-71q0-42-29-71t-71-29q-42 0-71 29t-29 71q0 42 29 71t71 29Zm-500-20h160v-160H200v160Zm202-420h156l-78-126-78 126Zm78 0ZM360-340Zm340 80Z"/>
              </svg>
              {{ $t('Templates') }}
            </div>
          </NuxtLink>

          <a :href="`https://app.onvocado.com/register?templateId=${templatePost.id}`"
             class="button secondary small md:hidden">
            {{ $t('Use this template') }}
          </a>
        </div>
        <div class="max-w-6xl md:mb-8 flex justify-between items-end md:items-center container">
          <!-- Title -->
          <h1 v-if="templatePost.attributes.title" class="w-1/2 text-3xl lg:text-4xl 2xl:text-5xl font-bold">
            {{ templatePost.attributes.title }}
          </h1>

          <!-- Buttons -->
          <div class="w-1/2 hidden md:flex space-x-4 justify-end">
            <a :href="`https://app.onvocado.com/register?templateId=${templatePost.id}`" class="button secondary">
              {{ $t('Use this template') }}
            </a>
          </div>
        </div>

        <div class="max-w-6xl grid grid-cols-12 gap-6 mt-6 container">

          <!-- Template Preview Section -->
          <div class="col-span-12 sm:col-span-7 flex flex-col">

            <!-- Preview Mode Toggle -->
            <div class="mb-4 flex justify-between items-center">
              <h3 class="text-lg font-semibold">Template Preview</h3>
              <div class="flex items-center gap-2">
                <button
                  @click="togglePreviewMode"
                  :disabled="isLoadingTemplate || !widgetTemplate"
                  class="px-3 py-1 text-sm rounded-md border transition-colors"
                  :class="{
                    'bg-onvocado-primary text-white border-onvocado-primary': showWidgetPreview,
                    'bg-white text-gray-700 border-gray-300 hover:bg-gray-50': !showWidgetPreview,
                    'opacity-50 cursor-not-allowed': isLoadingTemplate || !widgetTemplate
                  }">
                  {{ showWidgetPreview ? 'Static View' : 'Interactive View' }}
                </button>
              </div>
            </div>

            <!-- Main Preview Area -->
            <div class="w-full rounded-lg border bg-body-gray-dark aspect-4/3 lg:aspect-3/2 overflow-hidden relative">

              <!-- Widget Preview Mode -->
              <template v-if="showWidgetPreview">
                <!-- Loading State -->
                <div v-if="isLoadingTemplate" class="w-full h-full flex items-center justify-center">
                  <div class="text-gray-500">Loading interactive preview...</div>
                </div>
                <!-- Error State -->
                <div v-else-if="templateError" class="w-full h-full flex items-center justify-center">
                  <div class="text-red-500">Failed to load interactive preview</div>
                </div>
                <!-- Widget Preview -->
                <WidgetStepPreview
                  v-else-if="widgetTemplate && widgetTemplate.steps && widgetTemplate.steps.length"
                  :widget="widgetTemplate"
                  :target-step-index="targetImgIndex"
                  class="w-full h-full"/>
                <!-- No Widget Data -->
                <div v-else class="w-full h-full flex items-center justify-center">
                  <div class="text-gray-500">No interactive preview available</div>
                </div>
              </template>

              <!-- Static Image Mode -->
              <BlurHashResponsivePicture
                v-else-if="templatePost.attributes.thumbnails.data[targetImgIndex]"
                class="w-full h-full"
                :img-class="'!object-contain'"
                :image="templatePost.attributes.thumbnails.data[targetImgIndex]"/>
            </div>

            <!-- Thumbnail Navigation -->
            <section class="p-2 md:p-4 lg:p-6 grid grid-cols-4 gap-2 md:gap-4">
              <button
                v-for="(img, index) in templatePost.attributes.thumbnails.data"
                :key="index"
                class="relative block overflow-hidden rounded-lg shadow-lg hover:shadow-xl bg-body-gray-dark transition-all"
                @click="setTargetImage(index)"
                :class="{'border-2 border-onvocado-primary': targetImgIndex === index}">

                <!-- Static Thumbnail -->
                <template v-if="!showWidgetPreview">
                  <BlurHashResponsivePicture v-if="img"
                                             class="w-full rounded-lg border bg-body-gray-dark aspect-4/3 md:aspect-3/2 overflow-hidden"
                                             :img-class="'!object-contain'"
                                             :image="img"/>
                </template>

                <!-- Widget Thumbnail -->
                <template v-else>
                  <div class="w-full aspect-4/3 md:aspect-3/2 bg-body-gray-dark rounded-lg border overflow-hidden">
                    <WidgetStepPreview
                      v-if="widgetTemplate && widgetTemplate.steps && widgetTemplate.steps.length"
                      :widget="widgetTemplate"
                      :target-step-index="index"
                      class="w-full h-full"/>
                    <div v-else class="w-full h-full flex items-center justify-center">
                      <div class="text-gray-400 text-xs">No preview</div>
                    </div>
                  </div>
                </template>
              </button>
            </section>
          </div>

          <!-- Description & Buttons -->
          <div class="col-span-12 sm:col-span-5">
            <!-- About the Template -->
            <div class="text-gray-700 mb-4 markdown" v-html="templatePost.attributes.content">
            </div>

            <!-- Interactive Preview Status -->
            <div v-if="templatePost.attributes.backend_id" class="mb-4 p-3 rounded-lg border">
              <h4 class="font-semibold text-sm mb-2">Interactive Preview</h4>
              <div class="flex items-center gap-2 text-sm">
                <div v-if="isLoadingTemplate" class="flex items-center gap-2 text-blue-600">
                  <div class="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                  Loading...
                </div>
                <div v-else-if="templateError" class="text-red-600">
                  ❌ Failed to load
                </div>
                <div v-else-if="widgetTemplate" class="text-green-600">
                  ✅ Interactive preview available
                </div>
                <div v-else class="text-gray-500">
                  ⏳ No interactive data
                </div>
              </div>
            </div>

            <!-- Template Info -->
            <div class="text-sm mb-4">
              <h4 class="font-semibold">{{ $t('Categories') }}</h4>
              <ul v-for="(category, index) in templatePost.attributes.template_categories.data"
                  class="text-gray-500"
                  :key="index">
                <li>{{ category.attributes.title }}</li>
              </ul>
            </div>
          </div>

        </div>
      </section>

      <SectionRelatedTemplates v-if="relatedPosts.length > 1" :posts="relatedPosts" :currentPostID="templatePost.id"/>

    </template>

    <PageNotFound v-else/>

    <Footer/>

    <SEO :seo="templatePost?.attributes?.SEO"
         :created-at="templatePost?.attributes?.createdAt"
         :updated-at="templatePost?.attributes?.updatedAt"></SEO>

  </PreviewModeWrapper>
</template>

<script>
import templatePostQuery from "~/apollo/queries/templates/post";
import templatePostsQuery from "~/apollo/queries/templates/posts";
import lenisScrollMixin from "@/mixins/lenisScrollMixin";
import previewModeMixin from "@/mixins/previewModeMixin";
import fetchPostMixin from "@/mixins/fetchPostMixin";
import seoMixin from "@/mixins/seoMixin";
import { retryApolloQuery } from '@/utils/retryApolloQuery';
import WidgetStepPreview from '@/libs/widget-step-preview.common.js';
import '@/libs/widget-step-preview.css';

export default {
  name: 'TemplatePostSlug',
  colorMode: 'light',
  components: {
    WidgetStepPreview,
  },
  mixins: [lenisScrollMixin, previewModeMixin, fetchPostMixin, seoMixin],
  async asyncData({app, route, store}) {
    const previewMode = store.state.previewMode;

    if (previewMode) {
      await app.apolloProvider.defaultClient.resetStore();
    }

    try {
      // Fetch templatePost data with retry mechanism
      const templatePostResponse = await retryApolloQuery(
        app.apolloProvider.defaultClient,
        {
          query: templatePostQuery,
          variables: {
            slug: route.params.slug,
            locale: app.i18n?.locale || 'en',
          },
        },
        {
          maxAttempts: 5,
          onRetry: (error, attempt, delay) => {
            console.log(`Template post query retry ${attempt}/5 for slug: ${route.params.slug}`)
          },
          fallbackData: { data: { templatePosts: { data: [] } } }
        }
      );

      // Check if we have valid data
      if (!templatePostResponse?.data?.templatePosts?.data?.length) {
        console.warn(`No template post found for slug: ${route.params.slug}`);
        return { templatePost: null, relatedPosts: null, previewMode };
      }

      const templatePost = templatePostResponse.data.templatePosts.data[0];

      // Set route parameters for localization
      app.store.dispatch(
        "i18n/setRouteParams",
        Object.fromEntries(
          templatePost.attributes.localizations.data.map((el) => [
            el.attributes.locale,
            {slug: el.attributes.slug},
          ])
        )
      );

      // Fetch relatedPosts data with retry mechanism
      let relatedPostsResponse = await retryApolloQuery(
        app.apolloProvider.defaultClient,
        {
          query: templatePostsQuery,
          variables: {
            locale: app.i18n?.locale || 'en',
          },
        },
        {
          maxAttempts: 3,
          onRetry: (error, attempt) => {
            console.log(`Related posts query retry ${attempt}/3`)
          },
          fallbackData: { data: { templatePosts: { data: [] } } }
        }
      );

      const relatedPosts = relatedPostsResponse.data.templatePosts.data;

      return {
        templatePost,
        relatedPosts,
        previewMode
      }
    } catch (error) {
      console.error('Error in template post asyncData:', error);
      return { templatePost: null, relatedPosts: null, previewMode };
    }
  },
  data() {
    return {
      templatePost: null,
      relatedPosts: null,
      targetImgIndex: 0,
      // Widget template functionality
      widgetTemplate: null,
      isLoadingTemplate: false,
      templateError: null,
      showWidgetPreview: false, // Toggle between static images and widget preview
    };
  },
  async mounted() {
    // Fetch widget template data when component mounts
    if (this.templatePost?.attributes?.backend_id) {
      await this.fetchWidgetTemplate();
    }
  },
  created() {
    const preview = this.$route.query.preview;

    if (preview && !this.templatePost) {
      // Set color as it doesn't automatically set after redirect
      this.$colorMode.preference = 'dark';
      this.fetchPost('templatePosts', templatePostQuery).then(templatePostData => {
        this.templatePost = templatePostData;

        // Fetch widget template after getting template post data
        if (templatePostData?.attributes?.backend_id) {
          this.fetchWidgetTemplate();
        }

        this.fetchRelatedPosts(templatePostData.attributes.template_categories.data.map(tag => tag.attributes.title)).then(relatedPostsData => {
          this.relatedPosts = relatedPostsData;
          this.$nextTick(() => {
            this.previewMode = true;
          });
        });

        this.$nextTick(() => {
          this.previewMode = true;
        });
      });
    }
  },
  methods: {
    // Widget template functionality
    async fetchWidgetTemplate() {
      if (!this.templatePost?.attributes?.backend_id) {
        console.warn('No backend_id found for template');
        return;
      }

      this.isLoadingTemplate = true;
      this.templateError = null;

      try {
        const widgetTemplate = await this.$axios.$get(`/v1/api/widget-templates/${this.templatePost.attributes.backend_id}/`);
        this.widgetTemplate = widgetTemplate;
      } catch (error) {
        console.error(`Failed to fetch widget template ${this.templatePost.attributes.backend_id}:`, error);
        this.templateError = error;
        this.widgetTemplate = null;
      } finally {
        this.isLoadingTemplate = false;
      }
    },
    togglePreviewMode() {
      this.showWidgetPreview = !this.showWidgetPreview;
    },
    setTargetImage(index) {
      if (index >= 0 && index < this.templatePost.attributes.thumbnails.data.length) {
        this.targetImgIndex = index;
      }
    },
    getPageData() {
      return this.templatePost;
    },
  },
  beforeRouteLeave(to, from, next) {
    // Reset color as it doesn't automatically set after redirect (preview)
    this.$colorMode.preference = 'light';
    next()
  },
};
</script>
