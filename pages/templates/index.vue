<template>
  <PreviewModeWrapper :previewMode="previewMode">

    <!-- Menu spacer -->
    <MenuSpacer/>

    <section class="xl:max-w-5xl container">
      <div class="my-12 md:my-0 md:min-h-[40vh] flex flex-col justify-center md:items-center">
        <h1 class="mb-8">{{ templates.data?.attributes?.title }}</h1>
        <div v-html="templates.data?.attributes?.details" class="mb-8 md:mx-32 md:text-center text-lg"></div>
      </div>
    </section>

    <!-- Modal Component -->
    <TemplateModal v-if="previewTemplate"
                   :template="templatesPosts.data[targetTemplatePostIndex]"
                   :total-templates="templatesPosts.data.length"
                   :current-index="targetTemplatePostIndex"
                   @template-change="index => setTargetTemplateIndex(index)"
                   @close="closeModal"/>

    <!-- Widget Gallery Component -->
    <WidgetGallery class="pb-12 md:py-24 container"
                   :templates="templatesPosts"
                   @preview-widget="triggerPreviewWidget"/>

    <Footer/>

    <SEO :seo="templates.data?.attributes?.SEO"
         :created-at="templates.data?.attributes?.createdAt"
         :updated-at="templates.data?.attributes?.updatedAt"></SEO>
  </PreviewModeWrapper>
</template>

<script>
import templatesPostsQuery from "~/apollo/queries/templates/posts";
import templatesQuery from "@/apollo/queries/pages/templates.gql";
import lenisScrollMixin from '@/mixins/lenisScrollMixin';
import previewModeMixin from '@/mixins/previewModeMixin';
import TemplateModal from "~/components/TemplateModal.vue";
import WidgetGallery from '~/components/WidgetGallery/WidgetGallery.vue';
import seoMixin from "@/mixins/seoMixin";

export default {
  name: 'templates',
  components: {WidgetGallery, TemplateModal},
  colorMode: 'dark',
  mixins: [lenisScrollMixin, previewModeMixin, seoMixin],
  data() {
    return {
      templates: null,
      templatesPosts: null,
      previewTemplate: false,
      targetTemplatePostIndex: null,
      availableFonts: [
        "Anton", "Be Vietnam Pro", "Bitter", "Chakra Petch", "Hahmlet",
        "Inconsolata", "Inter", "Kosugi", "Lato", "Lora", "M PLUS 1 Code",
        "Montserrat", "Noto Sans", "Noto Serif", "Nunito", "Open Sans",
        "PT Sans", "PT Serif", "Pridi", "Raleway", "Roboto",
        "Roboto Slab", "Shadows Into Light", "Source Sans Pro", "Titillium Web",
        "Ubuntu Mono"
      ]
    };
  },
  async asyncData({app, store}) {
    const previewMode = store.state.previewMode;

    if (previewMode) {
      await app.apolloProvider.defaultClient.resetStore();
    }

    const responseTemplates = await app.apolloProvider.defaultClient.query({
      query: templatesQuery,
      variables: {
        locale: app.i18n?.locale || 'en',
      },
    }).catch((err) => {
      console.log(JSON.stringify(err, null, 2));
    });

    const templates = responseTemplates.data.templates;

    const responseTemplatesPosts = await app.apolloProvider.defaultClient.query({
      query: templatesPostsQuery,
      variables: {
        locale: app.i18n?.locale || 'en',
        publicationState: previewMode ? "PREVIEW" : "LIVE",
      },
    }).catch((err) => {
      console.log(JSON.stringify(err, null, 2));
    });

    const templatesPosts = responseTemplatesPosts.data.templatePosts;

    // Widget templates will now be fetched on-demand when needed
    return {
      templates,
      previewMode,
      templatesPosts,
    };
  },
  methods: {
    async fetchWidgetTemplate(backendId) {
      try {
        const widgetTemplate = await this.$axios.$get(`/v1/api/widget-templates/${backendId}/`);
        return widgetTemplate;
      } catch (error) {
        console.error(`Failed to fetch widget template ${backendId}:`, error);
        return null;
      }
    },
    loadGoogleFonts() {
      const fontFamilies = this.availableFonts
        .map(font => font.replace(/ /g, "+") + ":wght@400;700")
        .join("&family=");

      const fontURL = `https://fonts.googleapis.com/css2?family=${fontFamilies}&display=swap`;

      // Check if the font link already exists to prevent duplicate loads
      if (!document.querySelector(`link[href="${fontURL}"]`)) {
        const link = document.createElement("link");
        link.href = fontURL;
        link.rel = "stylesheet";
        link.as = "style";
        link.onload = () => (link.rel = "stylesheet");
        document.head.appendChild(link);
      }
    },
    triggerPreviewWidget(id) {
      // Find the index of the template within templatesPosts by matching the ID
      const index = this.templatesPosts.data.findIndex((item) => {
        return String(item.attributes.backend_id) === String(id);
      });

      // If the template does not exist, gracefully exit
      if (index === -1) {
        console.error(`Template with id ${id} not found in templatesPosts`);
        return;
      }

      // Update state with the found index and open the modal
      this.setTargetTemplateIndex(index);
      this.setPreviewTemplate(true);

      // Get the template slug
      const template = this.templatesPosts.data[index];
      const slug = template.attributes.slug;

      // Update the URL using history.pushState
      history.pushState({
        modalOpen: true,
        templateIndex: index
      }, '', `${this.$i18n.locale === this.$i18n.defaultLocale ? '' : '/' + this.$i18n.locale}/templates/${slug}/`);
    },
    // Sets the index of the currently viewed template
    setTargetTemplateIndex(index) {
      this.targetTemplatePostIndex = index;
    },
    // Opens or closes the modal and updates scroll behavior
    setPreviewTemplate(value) {
      this.previewTemplate = value;
      this.$store.dispatch('triggerScrollDisabled', value);
    },
    // Closes the modal and navigates back in history
    closeModal() {
      this.setPreviewTemplate(false);
      history.back();
    },
    // Handles the popstate event for back/forward navigation
    handlePopState(event) {
      if (event.state && event.state.modalOpen) {
        // Open the modal with the correct template
        const index = event.state.templateIndex;
        this.setTargetTemplateIndex(index);
        this.setPreviewTemplate(true);
      } else {
        // Close the modal
        this.setPreviewTemplate(false);
      }
    },
    getPageData() {
      return this.templates?.data;
    },
  },
  mounted() {
    window.addEventListener("popstate", this.handlePopState);

    // Load Google Fonts dynamically
    this.loadGoogleFonts();

    const pathParts = this.$route.path.split("/");
    if (pathParts.length === 3 && pathParts[1] === "templates") {
      const slug = pathParts[2];
      const index = this.templatesPosts.data.findIndex(item => item.slug === slug);
      if (index !== -1) {
        history.replaceState({modalOpen: true, templateIndex: index}, '', this.$route.fullPath);
        this.setTargetTemplateIndex(index);
        this.setPreviewTemplate(true);
      }
    } else {
      history.replaceState({modalOpen: false}, "", this.$route.fullPath);
    }
  },
  beforeDestroy() {
    window.removeEventListener("popstate", this.handlePopState);
  }
}
</script>
